import { disputedData } from "./disputedMapdata";

function webMercatorToLonLat([x, y]: [number, number]): [number, number] {
  const RADIUS = 6378137;

  const lon = (x / RADIUS) * (180 / Math.PI);
  const lat = (y / RADIUS) * (180 / Math.PI);
  return [
    lon,
    (180 / Math.PI) * (2 * Math.atan(Math.exp(y / RADIUS)) - Math.PI / 2),
  ];
}

// Create GeoJSON features from disputedData
export const createDisputedFeatures = () => {
  const features: any = {
    type: "FeatureCollection",
    features: [],
  };

  Object.entries(disputedData).forEach(([name, regionData]: any) => {
    if (regionData.rings) {
      regionData.rings.forEach((ring: any) => {
        const geographicRing = ring.map(webMercatorToLonLat);

        // Ensure the polygon ring is closed
        if (
          geographicRing.length > 0 &&
          (geographicRing[0][0] !==
            geographicRing[geographicRing.length - 1][0] ||
            geographicRing[0][1] !==
              geographicRing[geographicRing.length - 1][1])
        ) {
          geographicRing.push(geographicRing[0]);
        }

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "Polygon",
            coordinates: [geographicRing],
          },
        });
      });
    }

    if (regionData.paths) {
      regionData.paths.forEach((path: any) => {
        const geographicPath = path.map(webMercatorToLonLat);

        features.features.push({
          type: "Feature",
          properties: { NAME: name, disputed: true },
          geometry: {
            type: "LineString",
            coordinates: geographicPath,
          },
        });
      });
    }
  });

  return features;
};
